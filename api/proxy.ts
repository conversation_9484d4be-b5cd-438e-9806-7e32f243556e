const BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models';
const DEFAULT_MODEL_NAME = 'gemini-pro';
const DEFAULT_FUNCTION_NAME = 'generateContent';
const MODEL_GET_PARAM = 'model';
const FUNCTION_GET_PARAM = 'function';

// https://vercel.com/docs/functions/edge-runtime
export const config = {
  runtime: 'edge',
  // https://vercel.com/docs/edge-network/regions#region-list
  regions: ['bom1', 'cle1', 'cpt1', 'gru1', 'hkg1', 'hnd1', 'iad1', 'icn1', 'kix1', 'pdx1', 'sfo1', 'sin1', 'syd1'],
};

export async function GET(request: Request) {
  const requestUrl = new URL(request.url);

  const urlWithParams = new URL(BASE_URL);

  for (const [key, value] of requestUrl.searchParams.entries()) {
    urlWithParams.searchParams.append(key, value);
  }

  try {
    return await fetch(urlWithParams.toString(), {
      method: 'GET',
      body: null,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (e) {
    console.log('Error making request:', e);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function POST(request: Request) {
  const requestUrl = new URL(request.url);

  const modelName = requestUrl.searchParams.get(MODEL_GET_PARAM) || DEFAULT_MODEL_NAME;
  const functionName = requestUrl.searchParams.get(FUNCTION_GET_PARAM) || DEFAULT_FUNCTION_NAME;

  const urlWithParams = new URL(`${BASE_URL}/${modelName}:${functionName}`);

  for (const [key, value] of requestUrl.searchParams.entries()) {
    if (key !== MODEL_GET_PARAM && key !== FUNCTION_GET_PARAM) {
      urlWithParams.searchParams.append(key, value);
    }
  }

  try {
    return await fetch(urlWithParams.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: request.body,
    });
  } catch (e) {
    console.log('Error making request:', e);
    return new Response('Internal Server Error', { status: 500 });
  }
}
